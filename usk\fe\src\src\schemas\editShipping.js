import MESSAGE from 'helpers/message';

const editShipping = {
  additionalProperties: false,
  type: 'object',
  required: [ 'grossWeight', 'date', 'quantity', 'tareWeight', 'netWeight'],
  properties: {
    destinationId: {
      type: 'integer',
      minimum: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    grossWeight: {
      type: 'string',
      // exclusiveMinimum: 0,
      // maximum: 999999,
      minLength: 1,
      errorMessage: {
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    tareWeight: {
      type: 'string',
      // minimum: 0,
      // maximum: 999999,
      // exclusiveMaximum: {
      //   $data: '1/grossWeight',
      // },
      errorMessage: {
        // minimum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MIN_ERROR,
        // maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
        // exclusiveMaximum: MESSAGE.MSG_LIMITS_TAREWEIGHT_MAX_ERROR,
        _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      },
    },
    quantity: {
      type: 'string',
      // minimum: 1,
      // maximum: 9999999,
      // errorMessage: {
      //   minimum: MESSAGE.MSG_LIMITS_QUANTITY_MIN_ERROR,
      //   maximum: MESSAGE.MSG_SAGE_NUM_ERROR,
      //   _: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
      // },
    },
    netWeight: {
      type: 'string',
    },
    date: {
      type: 'string',
      format: 'slash-date',
      errorMessage: {
        format: MESSAGE.MSG_LIMITS_DATE_ERROR,
      },
    },
  },
  errorMessage: {
    required: {
      name: MESSAGE.MSG_REQUIRED_INPUTFORM_ERROR,
    },
  },
};

export default editShipping;
